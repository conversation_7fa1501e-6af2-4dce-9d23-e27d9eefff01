import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'constants/app_theme.dart';
import 'constants/app_constants.dart';
import 'screens/login_screen.dart';
import 'screens/enhanced_dashboard_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/not_found_screen.dart';
import 'providers/auth_provider.dart';
import 'services/navigation_service.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  runApp(const AmalPointAdminApp());
}

class AmalPointAdminApp extends StatelessWidget {
  const AmalPointAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider(create: (_) => NavigationService()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        themeMode: ThemeMode.light,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('en', ''), // English
          Locale('bn', ''), // Bengali
        ],
        onGenerateRoute: (settings) {
          // Handle dynamic routes here
          return MaterialPageRoute(
            builder: (context) => const NotFoundScreen(),
          );
        },
        routes: {
          '/': (context) => const AuthWrapper(),
          '/login': (context) => const LoginScreen(),
          '/dashboard': (context) => const EnhancedDashboardScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        switch (authProvider.status) {
          case AuthStatus.uninitialized:
            return const SplashScreen();
          case AuthStatus.authenticated:
            return _buildAuthenticatedApp(context);
          case AuthStatus.unauthenticated:
          case AuthStatus.loading:
          default:
            return const LoginScreen();
        }
      },
    );
  }

  Widget _buildAuthenticatedApp(BuildContext context) {
    return Navigator(
      onGenerateRoute: (settings) {
        WidgetBuilder builder;
        switch (settings.name) {
          case '/':
          case '/dashboard':
            builder = (context) => const EnhancedDashboardScreen();
            break;
          case '/users':
            builder = (context) => const EnhancedDashboardScreen(); // Will show users management
            break;
          case '/resellers':
            builder = (context) => const EnhancedDashboardScreen(); // Will show resellers management
            break;
          case '/admins':
            builder = (context) => const EnhancedDashboardScreen(); // Will show admins management
            break;
          case '/products':
            builder = (context) => const EnhancedDashboardScreen(); // Will show products management
            break;
          case '/categories':
            builder = (context) => const EnhancedDashboardScreen(); // Will show categories management
            break;
          case '/posts':
            builder = (context) => const EnhancedDashboardScreen(); // Will show posts management
            break;
          case '/comments':
            builder = (context) => const EnhancedDashboardScreen(); // Will show comments management
            break;
          case '/content':
            builder = (context) => const EnhancedDashboardScreen(); // Will show content management
            break;
          case '/analytics':
            builder = (context) => const EnhancedDashboardScreen(); // Will show analytics
            break;
          case '/settings':
            builder = (context) => const EnhancedDashboardScreen(); // Will show settings
            break;
          case '/notifications':
            builder = (context) => const EnhancedDashboardScreen(); // Will show notifications
            break;
          case '/reports':
            builder = (context) => const EnhancedDashboardScreen(); // Will show reports
            break;
          default:
            builder = (context) => const NotFoundScreen();
        }

        return MaterialPageRoute(
          builder: builder,
          settings: settings,
        );
      },
    );
  }
}